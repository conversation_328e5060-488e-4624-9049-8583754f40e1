{
    "name": "Partner Reference in Views",
    "version": "********.0",
    "category": "Accounting",
    "summary": "Ajoute la référence partenaire dans les vues factures, paiements, commandes et écritures",
    "description": """
        Ce module ajoute la référence du partenaire dans plusieurs vues :

        Lignes d'écriture comptable (account.move.line) :
        - Réf Partenaire : Référence du partenaire (partner_id.ref)
        - Code Cpte : Code du compte comptable (account_id.code)

        Factures (account.move) :
        - Réf Partenaire : Référence du partenaire avant le nom

        Paiements (account.payment) :
        - Réf Partenaire : Référence du partenaire avant le nom

        Commandes de vente (sale.order) :
        - Réf Partenaire : Référence du partenaire avant le nom
    """,
    "author": "<PERSON><PERSON><PERSON> - D&D Consulting",
    "website": "",
    "depends": ["account", "sale"],
    "data": [
        "views/account_move_line_view.xml",
        "views/account_move_view.xml",
        "views/account_payment_view.xml",
        "views/sale_order_view.xml"
    ],
    "installable": True,
    "application": False,
    "auto_install": False,
    "license": "LGPL-3",
}
