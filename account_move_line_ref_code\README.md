# Module Partner Reference in Views

## Description
Ce module ajoute la référence du partenaire dans plusieurs vues d'Odoo 17 Community :

- **Lignes d'écriture comptable** : Réf Partenaire + Code Compte
- **Factures** : Réf Partenaire  
- **Paiements** : Réf Partenaire
- **Commandes de vente** : Réf Partenaire

## Installation

1. <PERSON><PERSON><PERSON> le dossier `account_move_line_ref_code` dans votre répertoire `addons/` ou `custom_addons/`

2. Redémarrez votre serveur Odoo

3. Mettez à jour la liste des modules :
   - Apps → Update Apps List

4. Installez le module :
   - Recherchez "Partner Reference in Views"
   - Cliquez sur "Install"

## Utilisation

Après installation, vous verrez la colonne "Réf Partenaire" dans :

- Comptabilité → Factures clients/fournisseurs
- Comptabilité → Paiements  
- Ventes → Commandes/Devis
- Comptabilité → Écritures comptables (avec Code Compte)

Les colonnes sont masquables via le menu des colonnes (⚙️).

## Dépannage

Si vous rencontrez des erreurs lors de l'installation :

1. Vérifiez que les modules `account` et `sale` sont installés
2. Redémarrez le serveur Odoo
3. Essayez de mettre à jour le module

## Support

Module développé par Brahim Chabane - D&D Consulting
